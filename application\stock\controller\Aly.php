<?php
namespace app\stock\controller;

use think\Controller;
use think\Request;
use think\Db;
use think\Config;
use think\Session;
use think\Cache;

class Aly extends Controller
{
   // private $_apiUrl = 'http://alirm-com.konpn.com';

   private $_apiUrl = 'https://finance.market.alicloudapi.com';
   
    
    private $_tradeType = '';
    private $_apiType = '';
    
    public function _initialize() {
        $type = Config::get('site.api_type');
        $this->_tradeType = Config::get('site.trade_type');
        $this->_apiType = $type;
    }
    
    public function start() {

        $code = 'BTC';
        $type = '1M';
        $start = date('Y-m-d H:i:s', time() - 60*500 + 1);
        $end = date('Y-m-d H:i:s', time());

        $url = $this->_apiUrl . "/query/comlstkm?fromtick=".time()."&period=1M,5M&symbol=".$code;
        $result = stock_request($url);
        fh($result['Obj'][0]);
    }

    /**
     * 检查产品表配置
     */
    public function checkProducts() {
        echo "=== 产品表配置检查 ===" . PHP_EOL;

        // 检查所有产品
        $allProducts = Db::name('product')->select();
        echo "所有产品数量: " . count($allProducts) . PHP_EOL;

        // 检查启用的产品
        $activeProducts = Db::name('product')->where(array('status'=>1, 'is_open'=>1))->select();
        echo "启用的产品数量: " . count($activeProducts) . PHP_EOL;

        // 检查不同分类的产品分布
        for ($i = 1; $i <= 5; $i++) {
            $products = Db::name('product')->where(array('status'=>1, 'is_open'=>1, 'cid'=>$i))->select();
            echo "分类ID={$i}的产品数量: " . count($products) . PHP_EOL;
            if (!empty($products)) {
                echo "  产品列表: ";
                foreach ($products as $p) {
                    echo "{$p['code']} ";
                }
                echo PHP_EOL;
            }
        }

        // 检查cid=2的产品（阿里云API使用的产品）
        $alyProducts = Db::name('product')->where(array('status'=>1, 'is_open'=>1, 'cid'=>2))->select();
        echo "阿里云API产品数量(cid=2): " . count($alyProducts) . PHP_EOL;

        if (!empty($alyProducts)) {
            echo "阿里云API产品列表:" . PHP_EOL;
            foreach ($alyProducts as $product) {
                echo "- ID: {$product['id']}, Code: {$product['code']}, Title: {$product['title']}" . PHP_EOL;
            }
        } else {
            echo "⚠️ 没有找到配置为阿里云API的产品(cid=2)！" . PHP_EOL;
            echo "请在后台产品管理中添加产品，并设置分类为阿里云API对应的分类" . PHP_EOL;
        }

        // 检查产品表结构
        echo PHP_EOL . "=== 产品表字段检查 ===" . PHP_EOL;
        try {
            $fields = Db::query("DESCRIBE " . config('database.prefix') . "product");
            foreach ($fields as $field) {
                echo "字段: {$field['Field']}, 类型: {$field['Type']}, 默认值: {$field['Default']}" . PHP_EOL;
            }
        } catch (Exception $e) {
            echo "获取表结构失败: " . $e->getMessage() . PHP_EOL;
        }
    }
    
    /**
     * 获取最新K线数据
     */
    public function runLastData() {
        
        $type = $this->request->param('type');
        
        $types = array('1min', '5min', '30min', '1hour', '1day');
        
        if (!in_array($type, $types)) {
            return ;
        }
        
        $time = time();
        $cmds = array();
        
        $path = dirname(dirname(dirname(dirname(__FILE__)))).'/stock.php ';
        
        $stocks = Db::name('product')->where(array('status'=>1, 'is_open'=>1, 'cid'=>2))->select();
        
        foreach ($stocks as $key=>$stock) {
            $code = $stock['code'];
            $cmds[] = 'php '.$path.'  Aly/getLastData/type/'.$type.'/code/'.$code;
        }
      
        pcntl_signal(SIGCHLD, SIG_IGN);	//如果父进程不关心子进程什么时候结束,子进程结束后，内核会回收。
        foreach ($cmds as  $cmd) {
            $pid = pcntl_fork();	//创建子进程
            //父进程和子进程都会执行下面代码
            if ($pid == -1) {
                //错误处理：创建子进程失败时返回-1.
                die('could not fork');
            } else if ($pid) {
                //父进程会得到子进程号，所以这里是父进程执行的逻辑
                //如果不需要阻塞进程，而又想得到子进程的退出状态，则可以注释掉pcntl_wait($status)语句，或写成：
                pcntl_wait($status, WNOHANG); //等待子进程中断，防止子进程成为僵尸进程。
            } else {
                //子进程得到的$pid为0, 所以这里是子进程执行的逻辑。
                echo shell_exec($cmd);
                exit(0) ;
            }
        }
        exit(0) ;
    }
    
    
      /**
     * 获取最新价数据脚本
     */
    public function runStock() {
        $stocks = Db::name('product')->where(array('status'=>1, 'is_open'=>1, 'cid'=>2  ))->select();
        $time = time();
        $cmds = array();
        $path = dirname(dirname(dirname(dirname(__FILE__)))).'/stock.php ';
        
        $runObj = array();
        foreach ($stocks as $key=>$val) {
            $runObj[] = $val['code'];
        }
        
        foreach ($runObj as $key=>$val) {
            $cmds[] = 'php '.$path.'  Aly/getStock/code/'.$val;
            
            //  $cmds[] = 'php '.$path.'  Aly/getStock/code/'.implode(',', $val);
            
            //var_dump($cmds);
        }
        
        pcntl_signal(SIGCHLD, SIG_IGN);	//如果父进程不关心子进程什么时候结束,子进程结束后，内核会回收。
        foreach ($cmds as  $cmd) {
            $pid = pcntl_fork();	//创建子进程
            //父进程和子进程都会执行下面代码
            if ($pid == -1) {
                //错误处理：创建子进程失败时返回-1.
                die('could not fork');
            } else if ($pid) {
                //父进程会得到子进程号，所以这里是父进程执行的逻辑
                //如果不需要阻塞进程，而又想得到子进程的退出状态，则可以注释掉pcntl_wait($status)语句，或写成：
                pcntl_wait($status, WNOHANG); //等待子进程中断，防止子进程成为僵尸进程。
            } else {
                //子进程得到的$pid为0, 所以这里是子进程执行的逻辑。
                echo shell_exec($cmd);
                exit(0) ;
            }
        }
        exit(0);
    }
    
    
      /**
     * 获取最新价数据脚本
     */
    public function runStock1() {
        $stocks = Db::name('product')->where(array('status'=>1, 'is_open'=>1, 'cid'=>3))->select();
        $time = time();
        $cmds = array();
        $path = dirname(dirname(dirname(dirname(__FILE__)))).'/stock.php ';
        
        $runObj = array();
        foreach ($stocks as $key=>$val) {
            $runObj[] = $val['code'];
        }
        
        foreach ($runObj as $key=>$val) {
            $cmds[] = 'php '.$path.'  Aly/getStock/code/'.$val;
            
            //  $cmds[] = 'php '.$path.'  Aly/getStock/code/'.implode(',', $val);
            
            //var_dump($cmds);
        }
        
        pcntl_signal(SIGCHLD, SIG_IGN);	//如果父进程不关心子进程什么时候结束,子进程结束后，内核会回收。
        foreach ($cmds as  $cmd) {
            $pid = pcntl_fork();	//创建子进程
            //父进程和子进程都会执行下面代码
            if ($pid == -1) {
                //错误处理：创建子进程失败时返回-1.
                die('could not fork');
            } else if ($pid) {
                //父进程会得到子进程号，所以这里是父进程执行的逻辑
                //如果不需要阻塞进程，而又想得到子进程的退出状态，则可以注释掉pcntl_wait($status)语句，或写成：
                pcntl_wait($status, WNOHANG); //等待子进程中断，防止子进程成为僵尸进程。
            } else {
                //子进程得到的$pid为0, 所以这里是子进程执行的逻辑。
                echo shell_exec($cmd);
                exit(0) ;
            }
        }
        exit(0);
    }  
    
    /**
     * 获取最新价数据脚本
     */
    // public function runStock() {
        
        
        
    //     $stocks = Db::name('product')->where(array('status'=>1, 'is_open'=>1, 'cid'=>2))->select();
        
        
    //   //  var_dump($stocks);
    //     $time = time();
    //     $cmds = array();
    //     $path = dirname(dirname(dirname(dirname(__FILE__)))).'/stock.php ';
        
    //     $runObj = array();
    //     foreach ($stocks as $key=>$val) {
            
    //         $runObj[floor($key/10)][] = $val['code'];
            
    //     }
        
    //     foreach ($runObj as $key=>$val) {
            
    //         $cmds[] = 'php '.$path.'  Aly/getStock/code/'.implode(',', $val);
            
            
    //         //var_dump($cmds);
    //     }
        
    //     pcntl_signal(SIGCHLD, SIG_IGN);	//如果父进程不关心子进程什么时候结束,子进程结束后，内核会回收。
    //     foreach ($cmds as  $cmd) {
    //         $pid = pcntl_fork();	//创建子进程
    //         //父进程和子进程都会执行下面代码
    //         if ($pid == -1) {
    //             //错误处理：创建子进程失败时返回-1.
    //             die('could not fork');
    //         } else if ($pid) {
    //             //父进程会得到子进程号，所以这里是父进程执行的逻辑
    //             //如果不需要阻塞进程，而又想得到子进程的退出状态，则可以注释掉pcntl_wait($status)语句，或写成：
    //             pcntl_wait($status, WNOHANG); //等待子进程中断，防止子进程成为僵尸进程。
    //         } else {
    //             //子进程得到的$pid为0, 所以这里是子进程执行的逻辑。
    //             echo shell_exec($cmd);
    //             exit(0) ;
    //         }
    //     }
    //     exit(0);
    // }

    /**
     * 获取商品价格信息
     */
    public function getStock() {
        $code = $this->request->param('code');
       // $url = $this->_apiUrl . "/query/comrms?symbols=".$code;
        
    // $url = $this->_apiUrl . "/query/comrms?symbols=".$code;  
     
    // var_dump($code);
     
     $url = $this->_apiUrl . "/neipan/real?symbol=".$code;
     
   //  $url = $this->_apiUrl . "/market/kline?prod_code=USZS.OTC&tick_count=256&period_type=14400&adjust_price_type=forward&fields=tick_at,open_px,close_px,high_px,low_px,turnover_volume,turnover_value,average_px,px_change,px_change_rate,avg_px,ma2";
    
        
        $result = stock_request($url);
        
      //  var_dump($result);
        
        // 调试输出
        echo "API返回结果: " . PHP_EOL;
        var_dump($result);

        if (!$result || !isset($result['data'])) {
            echo "API返回数据为空或格式错误" . PHP_EOL;
            return;
        }

        // 根据实际返回的数据结构进行解析
        $data = $result['data'];

        // 如果是单个股票数据
        if (isset($data['symbol']) || isset($data[0])) {
            $res = $this->getFormat($data);
            if ($res && isset($res['code'])) {
                Cache::set($res['code'].'_stock', serialize($res));
                echo "成功缓存: " . $res['code'] . PHP_EOL;
            }
        }
        // 如果是数组格式的数据
        elseif (is_array($data)) {
            foreach ($data as $key => $val) {
                $res = $this->getFormat($val);
                if ($res && isset($res['code'])) {
                    Cache::set($res['code'].'_stock', serialize($res));
                    echo "成功缓存: " . $res['code'] . PHP_EOL;
                }
            }
        }
        
        
        
    }
    
    
    /**
     * 获取最新K线数据
     * @return boolean
     */
    public function getLastData() {
        
        $code = $this->request->param('code');
        
        $type = $this->request->param('type');
        
        if (!in_array($type, array('1min', '5min', '30min', '1hour', '1day'))) {
            return false;
        }
        $map = array('1min'=>'1M', '5min'=>'5M', '30min'=>'30M', '1hour'=>'1H', '1day'=>'D');
        
      //  $url = $this->_apiUrl . "/query/comkm?pidx=1&period=".$map[$type]."&psize=500&withlast=1&symbol=".$code;
        
        $url = $this->_apiUrl . "/market/kline?prod_code=".$code.".OTC&tick_count={$rows}&period_type={$interval}&adjust_price_type=forward&fields=tick_at,open_px,close_px,high_px,low_px,turnover_volume,turnover_value,average_px,px_change,px_change_rate,avg_px,ma2";
        
      //  $url = "https://api-ddc-wscn.awtmt.com/market/kline?prod_code={$option_key}.OTC&tick_count={$rows}&period_type={$interval}&adjust_price_type=forward";
		//	$url .= "&fields=tick_at,open_px,close_px,high_px,low_px,turnover_volume,turnover_value,average_px,px_change,px_change_rate,avg_px,ma2";
			
        
        
        echo $url.PHP_EOL;
        $result = stock_request($url);
        $data = $this->getFormatLine($result, $code);
        if ($data) {
            $lastData =  array_slice($data, -1, 1);
            if ($lastData[0]) {
                $res = Cache::set($code.'_stock_new_'.$type, serialize($lastData[0]));
                if ($res)
                    echo $code.'_stock_new_'.$type.PHP_EOL;
                    
            }
            //存储K线行情数据
            $res = Cache::set($code.'_stock_'.$type, serialize($data));
            if ($res)
                echo $code.'_stock_'.$type.PHP_EOL;
        }

        
    }
    
    
    /**
     * 获取格式化K线数据
     * @param unknown $data
     * @return boolean|unknown[][]|mixed[][]
     */
    private function getFormatLine($data) {

        $result = array();
        
        
        var_dump($data);
        
        $res=$data;
        	if($res['code'] == 20000){
				$returnData = [];
				$dataList = $res['data']['candle'][$option_key.'.OTC']['lines'];
				
				
				
				foreach($dataList as $item){
					$temp = [];
					$temp[] = $item[9].'000';
					$temp[] = $item[1];
					$temp[] = $item[2];
					$temp[] = $item[3];
					$temp[] = $item[0];
					$temp[] = date('Y-m-d H:i:s',$item[9]);
					$returnData[] = $temp;
				}
				
				$vol = array_column($returnData,0);
                array_multisort($vol,SORT_ASC,$returnData);
                
               // var_dump($returnData);
                
			 //	return_success (['data'=>$returnData]);
			 	
			 	$data['data']=['data'=>$returnData];
				
			}
        
        
      //  if (!isset($data['data']) || !$data['data'])
        //    return false;
          // var_dump($data['data']['candle']);
           
           
           
           
           
           
    //     function return_success($data = 'success', $status = "success")
    // {
    //     header('Content-Type:application/json');
    //     header('Access-Control-Allow-Origin:*');
    //     header('Access-Control-Allow-Methods:POST,GET,OPTIONS,DELETE');
    //     header('Access-Control-Allow-Headers:x-requested-with,content-type');
    //     header('Access-Control-Allow-Headers:x-requested-with,content-type,Authorization');
    //     $var_name = 'data';
    //     if (is_string($data)) {
    //         //如果data是字符串，则返回message格式信息
    //         if(strstr($data,'success')){
    //             $data = L('api_success');
    //         }
    //         $message = $data;
    //         $var_name = 'message';
    //     }
    //     $data = compact($var_name);
    //     $status = [
    //         'status' => $status,
    //         'code' => 0    //0-正常
    //     ];
    //     $data = array_merge($status, $data);
    //     echo json_encode($data);
    //     die();
    // }
           
    
            
        foreach ($data['data'] as $key=>$val) {
            
            
            $time = $val['Tick'] * 1000;
            $result[$time] = array(
                'time' => $time,
                'open' => $val['O'],
                'close' => $val['C'],
                'high' => $val['H'],
                'low' => $val['L'],
                'volume' => $val['V'],
            );
        }
        
        
        
        
        
        return array_reverse($result);
    }
    
    
   
     
    /**
     * 格式化最新数据
     * @param unknown $data
     * @param unknown $code
     * @return boolean|unknown[]|string[]|NULL[]|mixed[]|boolean[]
     */
    // private function getFormat($data) {

    //     $result = array();
    //     if (!$data)
    //         return false;
    //     $code = $data['S'];
    //     $result['code'] = $code;
    //     $result['price_high'] = $data['H'];
    //     $result['price_low'] = $data['L'];
    //     $result['price'] = $data['P'];
    //     $result['vol'] = $data['V'];
    //     $result['price_update'] = date('Y-m-d H:i:s');
    //     $result['open_price'] = $data['O'];
    //     $result['time'] = date('Y-m-d H:i:s', $data['Tick']);
    //     $result['price_zf'] = $data['ZF'];

    //     if ($result)
    //         $res = Db::name('product')->where(array('code'=>$code))->update($result);
    //     return $result;
    // }
    
    /**
     * 格式化最新数据 - 适配阿里云API返回格式
     * @param array $data API返回的数据
     * @return boolean|array
     */
    private function getFormat($data) {
        $result = array();
        if (!$data || !is_array($data)) {
            return false;
        }

        echo "正在解析数据: " . PHP_EOL;
        var_dump($data);

        // 根据阿里云API实际返回的数据结构进行解析
        // 需要根据实际API返回的字段名进行调整

        // 如果数据是对象格式（关联数组）
        if (isset($data['symbol'])) {
            $result['code'] = $data['symbol'];
            $result['price'] = isset($data['price']) ? $data['price'] : (isset($data['last']) ? $data['last'] : 0);
            $result['price_high'] = isset($data['high']) ? $data['high'] : 0;
            $result['price_low'] = isset($data['low']) ? $data['low'] : 0;
            $result['open_price'] = isset($data['open']) ? $data['open'] : 0;
            $result['vol'] = isset($data['volume']) ? $data['volume'] : 0;
            $result['price_zf'] = isset($data['change_rate']) ? $data['change_rate'] : 0;
        }
        // 如果数据是数组格式（索引数组）
        elseif (is_numeric(key($data))) {
            // 这里需要根据实际API返回的数组结构进行调整
            // 暂时保留原有逻辑，但添加安全检查
            $result['code'] = isset($data[0]) ? $data[0] : '';
            $result['price'] = isset($data[2]) ? $data[2] : 0;
            $result['price_high'] = isset($data[5]) ? $data[5] : 0;
            $result['price_low'] = isset($data[6]) ? $data[6] : 0;
            $result['open_price'] = isset($data[7]) ? $data[7] : 0;
            $result['vol'] = isset($data[10]) ? $data[10] : 0;
            $result['price_zf'] = isset($data[19]) ? $data[19] : 0;
        }
        else {
            echo "未知的数据格式: " . PHP_EOL;
            var_dump($data);
            return false;
        }

        $result['price_update'] = date('Y-m-d H:i:s');
        $result['time'] = date('Y-m-d H:i:s');

        echo "解析后的结果: " . PHP_EOL;
        var_dump($result);

        // 更新数据库
        if ($result && !empty($result['code'])) {
            $res = Db::name('product')->where(array('code' => $result['code']))->update($result);
            echo "数据库更新结果: " . ($res ? "成功" : "失败") . PHP_EOL;
        }

        return $result;
    }
    
    
    /**
     * 获取24H前信息
     * @param unknown $code
     * @return mixed|boolean
     */
    private function get24HData($code) {
        
        $key = $code.'_stock_open';
        $openPrice = 0;
        $history = unserialize(Cache::get($key));
        $time = strtotime(" -24 hours", strtotime(date('Y-m-d H:i').':00'))*1000;
        if (isset($history[$time])) {
            $openPrice = $history[$time]['open'];
        }
        
        if ($openPrice > 0) {
            return $openPrice;
        }
        return false;
    }
    
    
    
}